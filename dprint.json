{"typescript": {"lineWidth": 999, "quoteStyle": "preferDouble", "jsx.quoteStyle": "preferDouble", "memberExpression.linePerExpression": true, "binaryExpression.linePerExpression": true}, "json": {}, "toml": {}, "excludes": ["**/node_modules", "**/*-lock.json"], "plugins": ["https://plugins.dprint.dev/typescript-0.95.8.wasm", "https://plugins.dprint.dev/json-0.20.0.wasm", "https://plugins.dprint.dev/toml-0.7.0.wasm"]}