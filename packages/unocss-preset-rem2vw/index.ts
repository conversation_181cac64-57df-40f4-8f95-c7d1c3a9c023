import { definePreset } from "@unocss/core";

type Options = {
  /**
   * Base font size
   *
   * @default 16
   */
  baseFontSize?: number;

  /**
   * 小数精度
   *
   * @default 5
   */
  presision?: number;
};

const remRE = /(-?[.\d]+)(rem|px)/g;

export const presetRem2VW = definePreset((options: Options = {}) => {
  const {
    baseFontSize = 16,
    presision = 5,
  } = options;

  return {
    name: "@unocss/preset-rem-to-px",
    postprocess: (util) => {
      for (const entry of util.entries) {
        const [k, v] = entry;
        if (typeof v === "string" && remRE.test(v)) {
          entry[1] = v.replace(remRE, (_, p1, p2) => {
            if (p2 === "px") {
              return Math.abs(+p1) <= 1
                ? `${p1}px`
                : `${((+p1 * 4) / 15).toFixed(presision)}vw`;
            }
            if (p2 === "rem") {
              return `${((+p1 * baseFontSize * 4) / 15).toFixed(presision)}vw`;
            }
            return _;
          });
        }
      }
    },
  };
});
