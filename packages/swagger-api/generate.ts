import { fetch, write } from "bun";
import path from "node:path";
import type { Spec } from "swagger-schema-official";
import { generateApi } from "swagger-typescript-api";

const file = (
  await fetch("https://fat-activity.yalla.games/Activity6/swagger/V1/swagger.json")
    .then((response) => response.json())
) as Spec;

const DIST_PATH = path.join(__dirname, "dist");

const gen = await generateApi({
  output: DIST_PATH,
  cleanOutput: true,
  spec: file,
  modular: true,
  generateClient: false,
  generateRouteTypes: true,
  generateUnionEnums: true,
});

const str1 = 'import type { Activity6 } from "./Activity6Route";';
const str2 = "export type SWAGGER_API = {";
const str3 = Object
  .keys(file.paths)
  .map((path) => {
    let name = path
      .split("/")
      .slice(2)
      .map((word) => word.at(0)!.toUpperCase() + word.slice(1))
      .join("");
    name = `Activity6.${name}Create`;
    return `"${path}":[${name}.RequestBody,${name}.ResponseBody];`;
  })
  .join("\n");
const str4 = "};";
const output = [str1, str2, str3, str4].join("\n");

await write(path.join(DIST_PATH, "index.ts"), output);
