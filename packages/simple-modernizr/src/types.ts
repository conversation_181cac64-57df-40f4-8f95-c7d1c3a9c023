export type FeatureDetects = {
  /* -------------------------------------------------------------------------- */
  canvas: boolean;
  todataurljpeg: boolean;
  todataurlpng: boolean;
  todataurlwebp: boolean;
  /* -------------------------------------------------------------------------- */
  picture: boolean;

  imgcrossorigin: boolean;
  imgsizes: boolean;
  imgsrcset: boolean;
  imglazyloading: boolean;

  apng: boolean;

  jpeg2000: boolean;
  jpegxr: boolean;

  webp: boolean;
  webpalpha: boolean;
  webpanimation: boolean;
  webplossless: boolean;

  avif: boolean;
  avifalpha: boolean;
  avifanimation: boolean;
  aviflossless: boolean;
  /* -------------------------------------------------------------------------- */
  intersectionobserver: boolean;
  mutationobserver: boolean;
  resizeobserver: boolean;
  /* -------------------------------------------------------------------------- */
};
export type FeatureNames = keyof FeatureDetects;
export type Detector = () => Promise<boolean>;
export interface Modernizr {
  buffer: Map<FeatureNames, Detector>;
  register(field: FeatureNames, detector: Detector): void;
  build(): Promise<FeatureDetects>;
}
export interface ModernizrStatic extends FeatureDetects {
  instance: Modernizr;
  allsettled: Promise<FeatureDetects>;
}
