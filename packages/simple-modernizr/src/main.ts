import type { Detector, FeatureDetects, FeatureNames, Modernizr, ModernizrStatic } from "./types";

class _Modernizr implements Modernizr {
  buffer = new Map<FeatureNames, Detector>();

  public register(field: FeatureNames, detector: Detector) {
    this.buffer.set(field, detector);
  }

  public async build() {
    const ks = Array.from(this.buffer.keys());
    const vs = await Promise.all(this.buffer.values().map((fn) => fn()));
    const ret = Object.fromEntries(ks.map((_, i) => [ks[i], vs[i]]));
    const html = document.documentElement;
    for (const [k, v] of Object.entries(ret)) {
      html.classList.toggle(k, v);
    }
    Object.assign(window.Modernizr, ret);
    return ret as unknown as FeatureDetects;
  }
}

const modernizr = new _Modernizr();
globalThis.Modernizr ??= {} as ModernizrStatic;
globalThis.Modernizr.instance = modernizr;
