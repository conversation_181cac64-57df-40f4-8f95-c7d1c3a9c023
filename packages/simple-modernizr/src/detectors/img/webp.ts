Modernizr.instance.register("webp", async () => {
  try {
    const mute = Promise.withResolvers<boolean>();
    const image = new Image();
    image.onload = () => mute.resolve(image.width === 1);
    image.onerror = () => mute.resolve(false);
    image.src = "data:image/webp;base64,UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAAwA0JaQAA3AA/vuUAAA=";
    return mute.promise;
  } catch (error) {
    return false;
  }
});

Modernizr.instance.register("webpalpha", async () => {
  try {
    const mute = Promise.withResolvers<boolean>();
    const image = new Image();
    image.onload = () => mute.resolve(image.width === 1);
    image.onerror = () => mute.resolve(false);
    image.src = "data:image/webp;base64,UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAABBxAR/Q9ERP8DAABWUDggGAAAADABAJ0BKgEAAQADADQlpAADcAD++/1QAA==";
    return mute.promise;
  } catch (error) {
    return false;
  }
});

Modernizr.instance.register("webpanimation", async () => {
  try {
    const mute = Promise.withResolvers<boolean>();
    const image = new Image();
    image.onload = () => mute.resolve(image.width === 1);
    image.onerror = () => mute.resolve(false);
    image.src = "data:image/webp;base64,UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA";
    return mute.promise;
  } catch (error) {
    return false;
  }
});

Modernizr.instance.register("webplossless", async () => {
  try {
    const mute = Promise.withResolvers<boolean>();
    const image = new Image();
    image.onload = () => mute.resolve(image.width === 1);
    image.onerror = () => mute.resolve(false);
    image.src = "data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=";
    return mute.promise;
  } catch (error) {
    return false;
  }
});
