Modernizr.instance.register("avif", async () => {
  try {
    const mute = Promise.withResolvers<boolean>();
    const image = new Image();
    image.onload = () => mute.resolve(image.width === 1);
    image.onerror = () => mute.resolve(false);
    image.src = "data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAAEcbWV0YQAAAAAAAABIaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGNhdmlmIC0gaHR0cHM6Ly9naXRodWIuY29tL2xpbmstdS9jYXZpZgAAAAAeaWxvYwAAAAAEQAABAAEAAAAAAUQAAQAAABcAAAAqaWluZgEAAAAAAAABAAAAGmluZmUCAAAAAAEAAGF2MDFJbWFnZQAAAAAOcGl0bQAAAAAAAQAAAHJpcHJwAAAAUmlwY28AAAAQcGFzcAAAAAEAAAABAAAAFGlzcGUAAAAAAAAAAQAAAAEAAAAQcGl4aQAAAAADCAgIAAAAFmF2MUOBAAwACggYAAYICGgIIAAAABhpcG1hAAAAAAAAAAEAAQUBAoMDhAAAAB9tZGF0CggYAAYICGgIIBoFHiAAAEQiBACwDoA=";
    return mute.promise;
  } catch (error) {
    return false;
  }
});

Modernizr.instance.register("avifalpha", async () => {
  return false;
});

Modernizr.instance.register("avifanimation", async () => {
  return false;
});

Modernizr.instance.register("aviflossless", async () => {
  return false;
});
