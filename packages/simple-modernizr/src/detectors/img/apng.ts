Modernizr.instance.register("apng", async () => {
  try {
    const mute = Promise.withResolvers<boolean>();
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d")!;
    const image = new Image();
    image.onload = () => {
      if (typeof canvas.getContext === "undefined") {
        mute.resolve(false);
      } else {
        ctx.drawImage(image, 0, 0);
        mute.resolve(ctx.getImageData(0, 0, 1, 1).data[3] === 0);
      }
    };
    image.onerror = () => mute.resolve(false);
    image.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACGFjVEwAAAABAAAAAcMq2TYAAAANSURBVAiZY2BgYPgPAAEEAQB9ssjfAAAAGmZjVEwAAAAAAAAAAQAAAAEAAAAAAAAAAAD6A+gBAbNU+2sAAAARZmRBVAAAAAEImWNgYGBgAAAABQAB6MzFdgAAAABJRU5ErkJggg==";
    return mute.promise;
  } catch (error) {
    return false;
  }
});
