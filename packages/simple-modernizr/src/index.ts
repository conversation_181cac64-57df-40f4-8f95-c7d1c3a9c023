import "./main";
import "./detectors/img/apng";
import "./detectors/img/avif";
import "./detectors/img/base";
import "./detectors/img/webp";
import "./detectors/observer/intersection";
import "./detectors/observer/mutation";
import "./detectors/observer/resize";
import type { ModernizrStatic } from "./types";

globalThis.Modernizr.allsettled = globalThis.Modernizr.instance.build();

declare global {
  var Modernizr: ModernizrStatic;
}
