import { defineConfig } from "@rsbuild/core";
import { pluginBabel } from "@rsbuild/plugin-babel";
import { pluginSass } from "@rsbuild/plugin-sass";
import { pluginVue2 } from "@rsbuild/plugin-vue2";
import { pluginVue2Jsx } from "@rsbuild/plugin-vue2-jsx";
import { UnoCSSRspackPlugin } from "@unocss/webpack/rspack";
import fs from "node:fs/promises";
import path from "node:path";
import pxtoviewport from "postcss-px-to-viewport-8-plugin";
import { pluginGenImageMap } from "../plugins/gen-image-map";
import { pluginGenRouter } from "../plugins/gen-router";

export const ROOT_DIR = path.resolve(__dirname, "../");

export default defineConfig({
  source: {
    tsconfigPath: path.join(ROOT_DIR, "tsconfig.json"),
    preEntry: await Array.fromAsync(
      fs.glob(path.join(ROOT_DIR, "lib/*.{js,ts}")),
    ),
    include: [
      path.resolve(ROOT_DIR, "../simple-modernizr/"),
      /node_modules[\\/]@unocss[\\/](core)[\\/]/,
    ],
    assetsInclude: /\.svga$/,
    transformImport: [
      {
        libraryName: "vant",
        libraryDirectory: "es",
        style: true,
      },
    ],
  },
  output: {
    polyfill: "usage",
    copy: [
      ...await Array.fromAsync(
        fs.glob(path.join(ROOT_DIR, "lib/base/*.js")),
        file => ({
          from: file,
          to: "static/js",
        }),
      ),
    ],
  },
  html: {
    template: "src/index.html",
    inject: "body",
    mountId: "root",
    favicon: "https://file.yallaludo.com/puzzleHunter/favicon.ico",
    meta: {
      viewport: [
        "width=device-width",
        "initial-scale=1.0",
        "minimum-scale=1.0",
        "maximum-scale=1.0",
        "user-scalable=no",
        "viewport-fit=cover",
      ]
        .join(),
    },
    tags: [
      ...await Array.fromAsync(
        fs.glob(path.join(ROOT_DIR, "lib/base/*.js")),
        file => ({
          tag: "script",
          attrs: { defer: true, src: `static/js/${path.basename(file)}` },
          head: true,
        }),
      ),
    ],
  },
  performance: {
    chunkSplit: {
      strategy: "single-vendor",
      // forceSplitting: {
      //   axios: /node_modules[\\/]axios/,
      // },
    },
  },
  plugins: [
    pluginBabel({
      include: [/\.[jt]sx$/],
    }),
    pluginVue2(),
    pluginVue2Jsx({
      vueJsxOptions: {
        compositionAPI: true,
        functional: true,
      },
    }),
    pluginSass({
      sassLoaderOptions: {
        // additionalData: ['@use "@/styles/vars.scss" as *;'].join('\n'),
      },
    }),
    pluginGenRouter(),
    pluginGenImageMap({
      include: ["src/assets/image"],
      exclude: [],
    }),
  ],
  tools: {
    postcss(opt, { addPlugins }) {
      addPlugins([pxtoviewport({ viewportWidth: 375 })]);
    },
    rspack(config, { prependPlugins }) {
      prependPlugins(UnoCSSRspackPlugin());
      config.cache = false;
      config.optimization ??= {};
      config.optimization.realContentHash = true;
    },
  },
  environments: {
    production: {
      performance: {
        removeConsole: true,
      },
    },
    development: {
      source: {
        preEntry: await Array.fromAsync(
          fs.glob(path.join(ROOT_DIR, "lib/dev/*.[jt]s")),
        ),
      },
    },
  },
});
