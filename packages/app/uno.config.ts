import { presetRem2VW } from "@ludoh5/unocss-preset-rem2vw";
import { always, cond, equals } from "ramda";
import { defineConfig, presetAttributify, presetWind3, transformerAttributifyJsx, transformerDirectives, transformerVariantGroup } from "unocss";
import type { DynamicMatcher, DynamicShortcutMatcher } from "unocss";

const fontSizeRuleFactory: () => DynamicMatcher = () =>
(
  [, size],
  { symbols, shortcuts },
) => {
  // ? 对iOS环境指定字体大小时不生效，以免反直觉
  const isExplicit_iOS = shortcuts?.some(
    ([reg]) => reg instanceof RegExp && reg.test("ios:text-12"),
  );
  const ret = [
    {
      "font-size": `${+size!}px`,
    },
    {
      [symbols.parent]: "@supports (-webkit-touch-callout: none)",
      "font-size": `${+size! - 1}px`,
    },
  ];
  return isExplicit_iOS ? ret.slice(0, 1) : ret;
};

const textShadowRuleFactory: () => DynamicMatcher = () => ([_, match]) => ({
  "text-shadow": [
    `1px 0 ${match}`,
    `-1px 0 ${match}`,
    `0 1px ${match}`,
    `0 -1px ${match}`,
    `1px 1px ${match}`,
    `1px -1px ${match}`,
    `-1px 1px ${match}`,
    `-1px -1px ${match}`,
  ]
    .join(),
});

const textStrokeRuleFactory: () => DynamicShortcutMatcher = () => (match) => `relative z-0 after:(content-[attr(data-content)] abs-full -z-1 [-webkit-text-stroke:2px_${match[1]}])`;

const inlineLogicalValsRuleFactory: (
  name: "ms" | "me" | "ps" | "pe",
) => DynamicShortcutMatcher = (name) => (match) => {
  const [a, b] = cond([
    [equals("ms"), always(["ml", "mr"])],
    [equals("me"), always(["mr", "ml"])],
    [equals("ps"), always(["pl", "pr"])],
    [equals("pe"), always(["pr", "pl"])],
  ])(name);
  return [
    `[&:not(:lang(ar))]:${match[1]}${a}-${match[2]}`,
    `[&:lang(ar)]:${match[1]}${b}-${match[2]}`,
  ]
    .join(" ");
};

/* -------------------------------------------------------------------------- */

export default defineConfig({
  content: {
    pipeline: {
      include: [/\.(html|vue|s?css|[jt]sx)($|\?)/],
      exclude: [],
    },
  },
  presets: [
    presetWind3(),
    presetAttributify({ ignoreAttributes: ["font-size", "fill", "stroke"] }),
    presetRem2VW({ baseFontSize: 4 }),
  ],
  transformers: [
    transformerAttributifyJsx(),
    transformerDirectives({ enforce: "pre" }),
    transformerVariantGroup(),
  ],
  rules: [
    ["gdir", { direction: "var(--global-direction)" }],
    [/^text-(\d+\.?\d*)$/, fontSizeRuleFactory()],
    [/^text-shadows-(.+)$/, textShadowRuleFactory()],
  ],
  shortcuts: [
    /* ------------------------------- media query ------------------------------ */
    [/^ios:(.+)$/, (match) => `supports-[(-webkit-touch-callout:none)]:${match[1]}`],
    /* --------------------------------- normal --------------------------------- */
    ["abs-full", "absolute size-full top-0 left-0"],
    ["flex-fall", "flex flex-col items-center"],
    ["flex-center", "flex justify-center items-center"],
    ["inline-flex-center", "inline-flex justify-center items-center"],
    /* ---------------------------------- text ---------------------------------- */
    [/^text-stroke-(.+)$/, textStrokeRuleFactory()],
    ["line", "overflow-x-hidden text-ellipsis whitespace-nowrap py-1em -my-1em px-1"],
    /* ------------------------------- background ------------------------------- */
    ["bg-ccnr", "bg-(center contain no-repeat)"],
    [/^bg-v-(.+)$/, (match) => `bg-[image:var(--bg-${match[1]})]`],
    /* --------------------------- inline logical vals -------------------------- */
    [/^(-?)ms-(.+)$/, inlineLogicalValsRuleFactory("ms")],
    [/^(-?)me-(.+)$/, inlineLogicalValsRuleFactory("me")],
    [/^(-?)ps-(.+)$/, inlineLogicalValsRuleFactory("ps")],
    [/^(-?)pe-(.+)$/, inlineLogicalValsRuleFactory("pe")],
    ["text-start", "[&:not(:lang(ar))]:text-left [&:lang(ar)]:text-right"],
    ["text-end", "[&:not(:lang(ar))]:text-right [&:lang(ar)]:text-left"],
  ],
  variants: [
    (matcher) => {
      const val = matcher.match(/^(ar|en):/);
      if (!val) return;
      return {
        matcher: matcher.slice(3),
        selector: (s) => `#${val[1]} ${s}`,
      };
    },
  ],
});
