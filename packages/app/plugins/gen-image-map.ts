/**
 * 为引入的图片生成 webp/avif 版本
 */

import type { RsbuildPlugin, RsbuildPluginAPI } from "@rsbuild/core";
import crypto from "node:crypto";
import fs from "node:fs/promises";
import path from "node:path";
import sharp from "sharp";
import { RspackVirtualModulePlugin } from "./rspack-virtual-module";

// 缓存目录路径
const CACHE_DIR = path.resolve(__dirname, "../tmp/image-cache");
// 缓存结构
type ImageCache = Record<string, {
  avif?: string;
  webp?: string;
  base: string;
  hash: string;
}>;

export const pluginGenImageMap = (opts: {
  include: string[];
  exclude: string[];
}): RsbuildPlugin => ({
  name: "gen-image-clone",
  async setup(api: RsbuildPluginAPI) {
    // 确保缓存目录存在
    try {
      await fs.mkdir(CACHE_DIR, { recursive: true });
    } catch (e) {
      // 目录已存在或创建失败
    }

    let cache: ImageCache = {};
    const cacheFile = path.join(CACHE_DIR, "cache.json");

    // 读取缓存
    try {
      const cacheData = await fs.readFile(cacheFile, "utf-8");
      cache = JSON.parse(cacheData);
    } catch (e) {
      // 缓存文件不存在或解析失败，继续使用空缓存
    }

    const files = (await Promise.all(
      opts
        .include
        .map(p => path.resolve(__dirname, "../", p))
        .map(p => fs.readdir(p, { withFileTypes: true })),
    ))
      .flat()
      .filter(_ => !_.isDirectory())
      .filter(_ => !opts.exclude.some(p => path.resolve(_.parentPath, _.name).includes(p)));

    const assets = await Promise.all(files.map(async file => {
      const pathname = path.resolve(file.parentPath, file.name);
      const extname = path.extname(file.name);
      const basename = path.basename(file.name, extname);
      const content = await fs.readFile(pathname);
      const hash = crypto
        .createHash("md5")
        .update(content)
        .digest("hex")
        .slice(0, 8);

      return ({
        pathname,
        basename,
        extname: extname.slice(1),
        content,
        hash,
      });
    }));

    api.modifyRspackConfig(async (config) => {
      type ImageMap = Record<string, {
        avif?: string;
        webp?: string;
        base: string;
      }>;
      const obj = assets.reduce((acc, cur) => {
        const dir = `${config.output.publicPath ?? "/"}static/image/`;
        acc[cur.basename] = {
          base: `${dir}${cur.basename}.${cur.hash}.${cur.extname}`,
        };
        if (/(png|jpe?g)$/i.test(cur.extname)) {
          acc[cur.basename]!.avif = `${dir}${cur.basename}.${cur.hash}.avif`;
          acc[cur.basename]!.webp = `${dir}${cur.basename}.${cur.hash}.webp`;
        }
        return acc;
      }, {} as ImageMap);
      config.plugins.push(
        new RspackVirtualModulePlugin({
          "virtual-image-map": `export default ${JSON.stringify(obj)}`,
        }),
      );
    });

    api.processAssets(
      { stage: "additional" },
      async ({ sources, compilation }) => {
        const newCache: ImageCache = {};
        let cacheHitCount = 0;
        let generatedCount = 0;

        for (const item of assets) {
          const key = `${item.basename}.${item.hash}`;
          if (cache[key]) {
            // 使用缓存
            cacheHitCount++;
            const names = [
              `static/image/${item.basename}.${item.hash}.avif`,
              `static/image/${item.basename}.${item.hash}.webp`,
              `static/image/${item.basename}.${item.hash}.${item.extname}`,
            ];
            const cachedAsset = cache[key];
            if (cachedAsset.avif) {
              const avifPath = path.join(CACHE_DIR, cachedAsset.avif);
              const avifContent = await fs.readFile(avifPath);
              compilation.emitAsset(names[0]!, new sources.RawSource(avifContent));
            }
            if (cachedAsset.webp) {
              const webpPath = path.join(CACHE_DIR, cachedAsset.webp);
              const webpContent = await fs.readFile(webpPath);
              compilation.emitAsset(names[1]!, new sources.RawSource(webpContent));
            }
            const basePath = path.join(CACHE_DIR, cachedAsset.base);
            const baseContent = await fs.readFile(basePath);
            compilation.emitAsset(names[2]!, new sources.RawSource(baseContent));
            newCache[key] = cachedAsset;
          } else {
            // 生成新图片
            generatedCount++;
            if (/(png|jpe?g)$/i.test(item.extname)) {
              const names = [
                `static/image/${item.basename}.${item.hash}.avif`,
                `static/image/${item.basename}.${item.hash}.webp`,
                `static/image/${item.basename}.${item.hash}.${item.extname}`,
              ];

              const raw = sharp(item.content).raw();
              const buffers = await Promise.all([
                raw
                  .avif({
                    quality: 50,
                    // effort: 9, // 最大压缩
                    chromaSubsampling: "4:4:4", // 保色度
                  })
                  .toBuffer(),
                raw
                  .webp({
                    quality: 80,
                    // effort: 6,
                  })
                  .toBuffer(),
                item.extname.toLowerCase() === "png"
                  ? raw
                    .png({
                      compressionLevel: 9,
                      palette: true, // 8-bit palette 可能更小
                      // effort: 10,
                    })
                    .toBuffer()
                  : raw
                    .jpeg({
                      quality: 82, // 视觉无损的甜点值
                      progressive: true,
                      mozjpeg: true, // 启用 MozJPEG
                      optimiseCoding: true,
                      trellisQuantisation: true,
                      overshootDeringing: true,
                      quantisationTable: 3, // 3 为 MozJPEG 推荐量化表
                    })
                    .toBuffer(),
              ]);

              for (let i = 0; i < 3; i++) {
                const buffer = buffers[i]!;
                const name = names[i]!;
                // 保存到缓存目录
                const cachePath = path.join(CACHE_DIR, path.basename(name));
                await fs.writeFile(cachePath, buffer);
                compilation.emitAsset(name, new sources.RawSource(buffer));
              }

              newCache[key] = {
                avif: path.basename(names[0]!),
                webp: path.basename(names[1]!),
                base: path.basename(names[2]!),
                hash: item.hash,
              };
              console.log(`[pluginGenImageMap] Generated new images for ${item.basename}.${item.extname}`);
            } else {
              const name = `static/image/${item.basename}.${item.hash}.${item.extname}`;
              // 保存到缓存目录
              const cachePath = path.join(CACHE_DIR, path.basename(name));
              await fs.writeFile(cachePath, item.content);
              compilation.emitAsset(name, new sources.RawSource(item.content));
              newCache[key] = {
                base: path.basename(name),
                hash: item.hash,
              };
              console.log(`[pluginGenImageMap] Copied image ${item.basename}.${item.extname}`);
            }
          }
        }
        // 写入缓存
        await fs.writeFile(cacheFile, JSON.stringify(newCache, null, 2));
        console.log(`[pluginGenImageMap] Cache hit: ${cacheHitCount}, Generated: ${generatedCount}`);
      },
    );
  },
});
