import type { Rspack } from "@rsbuild/core";
import fs from "fs-extra";
import crypto from "node:crypto";
import path from "node:path";

export class RspackVirtualModulePlugin {
  #staticModules: Record<string, string>;

  #tempDir: string;

  constructor(staticModules: Record<string, string>, tempDir?: string) {
    this.#staticModules = staticModules;
    const nodeModulesDir = path.join(process.cwd(), "node_modules");
    if (!fs.existsSync(nodeModulesDir)) {
      fs.mkdirSync(nodeModulesDir);
    }

    if (!tempDir) {
      const hash = crypto
        .createHash("md5")
        .update(JSON.stringify(this.#staticModules))
        .digest("hex")
        .slice(0, 8);
      this.#tempDir = path.join(nodeModulesDir, `rspack-virtual-module-${hash}`);
    } else {
      this.#tempDir = path.join(nodeModulesDir, tempDir);
    }
    if (!fs.existsSync(this.#tempDir)) {
      fs.mkdirSync(this.#tempDir);
    }
  }

  apply(compiler: Rspack.Compiler) {
    // Write the modules to the disk
    for (const [path, content] of Object.entries(this.#staticModules)) {
      this.writeModule(path, content);
    }
    compiler.options.resolve.modules ??= ["node_modules"];
    compiler.options.resolve.modules.push(this.#tempDir);
    compiler.options.resolve.alias = {
      ...compiler.options.resolve.alias,
      ...Object
        .keys(this.#staticModules)
        .reduce(
          (acc, p) => {
            acc[p] = this.#normalizePath(p);
            return acc;
          },
          {} as Record<string, string>,
        ),
    };
    compiler.hooks.shutdown.tap("RspackVirtualModulePlugin", () => {
      this.clear();
    });
  }

  writeModule(file: string, content: string) {
    const normalizedPath = this.#normalizePath(file);
    fs.ensureDirSync(path.dirname(normalizedPath));
    fs.writeFileSync(normalizedPath, content);
  }

  clear() {
    fs.removeSync(this.#tempDir);
  }

  #normalizePath(p: string) {
    const ext = path.extname(p);
    return path.join(this.#tempDir, ext ? p : `${p}.js`);
  }
}
