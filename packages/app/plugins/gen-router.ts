/**
 * 根据 pages 目录自动生成路由配置
 * ? 示例
 * ? src/pages/
 * ? ├── _layout.vue          → /*
 * ? ├── _index.vue           → /
 * ? ├── about.vue            → /about
 * ? ├── blog/
 * ? │   └── _layout.vue      → /blog/*
 * ? │   └── _index.vue       → /blog/
 * ? │   └── [id].vue         → /blog/:id
 * ? │   └── _404.vue         → /blog/* 404 页面
 * ? └── _404.vue             → /* 404 页面
 */

import type { RsbuildPlugin, RsbuildPluginAPI } from "@rsbuild/core";
import fs from "node:fs/promises";
import path from "node:path";
import { RspackVirtualModulePlugin } from "./rspack-virtual-module";

export const pluginGenRouter = (): RsbuildPlugin => ({
  name: "gen-router",
  setup(api: RsbuildPluginAPI) {
    api.modifyRspackConfig(async (config) => {
      const root = path.resolve(__dirname, "../src/pages");
      const routes = await generate(root);

      // 创建虚拟模块
      config.plugins.push(
        new RspackVirtualModulePlugin({ "virtual-routes": routes }),
      );
      // config.module.rules?.push({
      //   scheme: "virtual",
      //   test: /virtual:routes/,
      //   type: "javascript/auto",
      // });
    });
  },
});

// 生成路由配置代码
async function generate(root: string) {
  const tree = (await scan(root))!;
  tree.name = "";
  const routes = serialize(tree);
  return `export default [${routes}];`;
}

type RouteUnit = {
  name: string;
  path: string;
  children?: RouteUnit[];
};

async function scan(root: string): Promise<RouteUnit | undefined> {
  const target = await fs.stat(root);

  if (target.isFile()) {
    const extname = path.extname(root);
    if (!/\.(vue|[jt]sx)$/.test(extname)) return;
    return ({
      name: path.basename(root, extname),
      path: root,
    });
  } else if (target.isDirectory()) {
    const pathnames = await fs.readdir(root);
    const children = (await Promise.all(
      pathnames.map((name) => scan(path.join(root, name))),
    ))
      .filter(Boolean)
      .toSorted((a) => a?.name.startsWith("_404.") ? 1 : -1) as RouteUnit[];
    return {
      name: path.basename(root),
      path: root,
      children,
    };
  }
}

function nameFilter(name: string) {
  if (name === "_index") return "";
  else if (name === "_404") return "*";
  else if (/\[\w+\]/.test(name)) return `:${name.slice(1, -1)}`;
  else return name;
}

function serialize(node: RouteUnit, prefix: string = "/") {
  const arr = [
    `name: "${prefix.replaceAll("/", ".")}${node.name}"`,
    `path: "${prefix}${nameFilter(node.name)}"`,
  ];
  if (node.children) {
    if (node.children.some(c => c.name === "_layout")) {
      const layout = node.children.find(c => c.name === "_layout")!;
      arr.push(`component: () => import("${layout.path}")`);
    } else if (node.children.some(c => c.name === "_index")) {
      arr.push(`redirect: "${prefix}"`);
    }
    arr.push(
      `children: [${
        node
          .children
          .map((n) => serialize(n, `${prefix}${nameFilter(node.name)}/`.replaceAll("//", "/")))
          .filter(Boolean)
          .join(",\n")
      }]`,
    );
  } else if (node.name === "_layout") {
    return "";
  } else {
    arr.push(`component: () => import("${node.path}")`);
  }

  return `{\n${arr.join(",\n")}\n}`;
}
