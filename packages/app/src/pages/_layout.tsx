import { defineComponent, getCurrentInstance } from "vue";
import { RouterView } from "vue-router";
import { TimeSpan } from "~/src/components/time-span";
import { showModalRecords } from "~/src/views/modal-records";
import { showModalRules } from "~/src/views/modal-rules";
import { showModalShare } from "~/src/views/modal-share";

export default defineComponent({
  name: "PageLayout",
  setup() {
    const inst = getCurrentInstance()!.proxy;

    return () => (
      <div class="relative w-full min-h-full flex-fall">
        <div class="fixed z-9999 top-30 left-0 right-0 w-50 ms-325 flex-fall items-end">
          <img
            class="w-49 h-44"
            src={getLocalImage("close-app-97x88")}
          />
        </div>

        <div class="absolute z-9 top-45 left-0 right-0 w-40 ms-0 flex-fall not-last:*:mb-12">
          <img
            class="size-37"
            src={getLocalImage("icon-share-74x74")}
            on-click={showModalShare}
          />
          <img
            class="size-37"
            src={getLocalImage("icon-q-74x74")}
            on-click={showModalRules}
          />
          <img
            class="size-37"
            src={getLocalImage("icon-record-74x74")}
            on-click={showModalRecords}
          />
        </div>

        <div
          class="w-296 h-128 mt-43 -mb-18 ar:bg-v-label-free-diamond-gift-ar-592x255"
          bg="v-label-free-diamond-gift-en-592x255 ccnr"
        />

        <div
          class="relative w-300 h-22.5 flex-center leading-22"
          bg="v-gradient-97x45 ccnr [length:100%_100%]"
          text="13 #FEF8D5 shadows-#AE0056"
        >
          <img
            class="absolute left-0 right-0 ms-28 size-21.5"
            src={getLocalImage("icon-clock-43x43")}
          />
          <span class="me-0.25ch">{inst.$tc("timeleft")}</span>
          <TimeSpan count={0} />
        </div>

        <RouterView />
      </div>
    );
  },
  i18n: {
    messages: {
      en: {
        timeleft: "Time Left:",
      },
      ar: {
        timeleft: "الوقت المتبقي:",
      },
    },
  },
});
