import { useLocalStorage } from "@vueuse/core";
import { computed, defineComponent, getCurrentInstance, type PropType } from "vue";
import { showModalGuide } from "~/src/views/modal-guide";

export default defineComponent({
  name: "PageIndex",
  setup() {
    const inst = getCurrentInstance()!.proxy;

    const selected = useLocalStorage(`${process.env.PUBLIC_NAME}:selected`, 0);

    return () => (
      <div class="w-full pt-5 flex-fall overflow-hidden">
        <div
          class="relative z-0 w-full h-418 flex-fall"
          after="content-empty absolute -z-1 w-416 h-418 ml-9 bg-(v-box-832x835 ccnr)"
        >
          <div
            class="mt-30 leading-24"
            text="19 #FEF8D5 shadows-#D01428"
          >
            {inst.$tc("daily_free_gifts")}
          </div>

          <div
            class="w-190 h-27.5 mt-33 -ml-8 flex-center"
            bg="v-title-380x55 ccnr"
          >
            {/* todo */}
          </div>

          <div class="w-300 mt-3 flex-(center wrap)">
            {Array.from({ length: 8 }).map((_, i) => (
              <div
                class="group w-67 [&:not(:nth-child(4n))]:me-10 mb-8"
                data-active={i === selected.value}
                on-click={() => (selected.value = i)}
                key={i}
              >
                <div
                  class="relative size-65 flex-center"
                  after="content-empty absolute size-65 bg-(v-bubble-131x130 ccnr) opacity-100 group-data-[active]:opacity-0 transition-(all duration-300)"
                  before="content-empty absolute size-94 bg-(v-bubble-188x185 ccnr) opacity-0 group-data-[active]:opacity-100 transition-(all duration-300)"
                >
                  <img
                    class="size-48"
                    src={""}
                  />
                </div>

                <div
                  class="w-67 h-19 mt-1 brightness-80 group-data-[active]:brightness-100 ar:bg-v-price-tag-ar-133x37"
                  bg="v-price-tag-en-133x37 ccnr"
                  transition="all duration-300"
                />
              </div>
            ))}
          </div>

          <button
            class="w-157 h-59 mt-12 flex-fall leading-52"
            bg="v-btn-yellow-333x117 ccnr"
          >
            <p text="22 #AB6E0F shadows-#FFF08B">{inst.$tc("collect")}</p>
          </button>
        </div>

        <div
          class="w-416 h-495 ms-3"
          bg="v-box-832x990 ccnr"
        >
          <div
            class="mt-28 leading-24"
            text="19 #FEF8D5 shadows-#D01428"
          >
            {inst.$tc("task")}
          </div>

          <TaskDetail
            class="mt-36 mb-6"
            type="token"
          />
          <TaskDetail
            class="mb-6"
            type="bubble"
          />
          <TaskDetail
            class="mb-6"
            type="pff"
          />
        </div>
      </div>
    );
  },
  i18n: {
    messages: {
      en: {
        daily_free_gifts: "Daily Free Gifts",
        task: "Tasks",
      },
      ar: {
        daily_free_gifts: "الهدايا المجانية اليومية",
        task: "المهام",
      },
    },
  },
});

const TaskDetail = defineComponent({
  name: "TaskDetail",
  props: {
    type: {
      type: String as PropType<"token" | "bubble" | "pff">,
      required: true,
    },
  },
  setup(props, ctx) {
    const inst = getCurrentInstance()!.proxy;

    const datalist = computed(() => Array.from({ length: 4 }));
    const count = computed(() => 0);
    const couldCollect = computed(() => false);

    return () => (
      <div class="mb-6 flex-fall">
        <div
          class="w-281 h-24 pb-2 flex-center"
          bg="v-title-571x48 ccnr"
          text="shadows-#94210C"
        >
          {inst.$tc(`win_${props.type}`)}
        </div>

        <div
          class="task-detail w-334 h-87 mt-1 pb-6 px-6"
          bg="v-box-668x174 ccnr"
        >
          <div
            class="size-51 flex-center"
            bg="v-pie-100x103 ccnr"
          >
            {/* todo */}
          </div>

          <div class="relative w-186">
            <p
              class="w-166 leading-13 whitespace-pre-line"
              text="11 start shadows-#94210C"
            >
              {inst.$tc(`days_${props.type}`, count.value)}
            </p>
            <div
              class="absolute top-0 left-0 right-0 ms-170 size-16"
              bg="v-icon-q-33x33 ccnr"
              on-click={showModalGuide}
            />
          </div>

          {props.type === "pff"
            ? (
              <div
                class="relative w-188 h-11 mt-6 -ms-6 flex-center ar:-scale-x-100"
                bg="v-progress-track4-375x22 ccnr [length:100%_100%]"
              >
                <div
                  class="absolute left-1 w-186 h-9"
                  bg="v-progress-bar4-371x18 ccnr [length:100%_100%]"
                />
              </div>
            )
            : (
              <div class="relative w-186 mt-6 -ms-6 flex-center justify-end">
                <div
                  class="relative w-188 h-11 flex-(center shrink-0) ar:-scale-x-100"
                  bg="v-progress-track3-375x22 ccnr [length:100%_100%]"
                >
                  <div
                    class="absolute left-1 w-186 h-9"
                    bg="v-progress-bar3-371x18 ccnr [length:100%_100%]"
                  />
                </div>

                <div class="absolute w-[calc(87%+18px)] -me-9 flex-center justify-between">
                  {datalist.value.map((_, i) => (
                    <div
                      class="relative size-20 flex-center rounded-full bg-#CB6111"
                      b="1 solid #F2D65C"
                      key={i}
                    >
                      <div
                        class="absolute -bottom-14"
                        text="11 shadows-#94210C"
                      >
                        {1}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {couldCollect.value
            ? (
              <button
                class="w-54 h-34 flex-fall leading-31"
                bg="v-btn-yellow-107x68 ccnr"
                on-click={console.log}
              >
                <p class="#AA6E1F shadows-#FEEF91">{inst.$tc("collect")}</p>
              </button>
            )
            : (
              <button
                class="w-54 h-34 flex-fall leading-31"
                bg="v-btn-green-107x68 ccnr"
                on-click={console.log}
              >
                <p text="#0E7E62 shadows-#6AF2CF">{inst.$tc("go")}</p>
              </button>
            )}
        </div>
      </div>
    );
  },
  i18n: {
    messages: {
      en: {
        win_token: "Win the Macaron Token",
        win_bubble: "Win the Macaron Bubble",
        win_pff: "Win the Childlike Joy Profile Frame",
        days_token: "Send the free diamond gift \nfor a total of 7 days ({count}/ 7)",
        days_bubble: "Receive the free diamond gift \nfor a total of 7 days ({count}/ 7)",
        days_pff: "Send the free diamond gift \nfor a total of 12 days ({count}/ 12)",
      },
      ar: {
        win_token: "الفوز بقطعة الشطرنج - ماكرون",
        win_bubble: "الفوز بفقاعة الدردشة - ماكرون",
        win_pff: "الفوز بإطار صورة الملف الشخصي - فرحة طفولية",
        days_token: "أرسل هدايا الماس المجانية لمدة 7 أيام بشكل تراكمي ({count}/ 7)",
        days_bubble: "احصل على هدايا الماس المجانية لمدة 7 أيام بشكل تراكمي ({count}/ 7)",
        days_pff: "أرسل هدايا الماس المجانية لمدة 12 يوما بشكل تراكمي ({count}/ 12)",
      },
    },
  },
});
