import { i18n } from "~/src/i18n";
import { useToggle } from "@vueuse/core";
import { Popover } from "vant";
import Vue, { defineComponent } from "vue";
import VueI18n from "vue-i18n";

// export const ModalContainer = defineComponent({
//   name: "ludo-modal-container",
//   setup(props, ctx) {
//     const [visible, toggleVisible] = useToggle(false);

//     return () => (
//       <Popover
//         value={visible.value}
//         on-close={() => toggleVisible(false)}
//       >
//         {ctx.slots.default}
//       </Popover>
//     );
//   },
// });

type ValidComponent = NonNullable<Parameters<typeof Vue.extend>[0]>;

export function useModal(options: {
  content: ValidComponent;
  parent?: Element;
}) {
  const Component = Vue.extend(options.content);
  let instance: InstanceType<typeof Component>;

  function create() {
    const localI18n = new VueI18n(Object.assign(
      {},
      options.content.i18n,
      { locale: i18n.locale },
    ));
    localI18n.mergeLocaleMessage(i18n.locale, i18n.messages[i18n.locale]);

    instance = new Component({
      el: document.createElement("div"),
      i18n: localI18n,
    });
    (options.parent ?? document.querySelector("#app"))!.appendChild(instance.$el);
    return instance;
  }

  function destroy() {
    instance?.$el.remove();
    instance?.$destroy();
  }

  return { create, destroy };
}
