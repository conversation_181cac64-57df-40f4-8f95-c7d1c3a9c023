/**
 * 增强版的 img 组件
 * * 1. 支持图片加载失败时的降级处理
 * * 2. 支持图片加载过程中的替换处理
 * * 3. 支持通过 IntersectionObserver 实现的懒加载
 * ! 请在组件外层监听点击事件
 */

import { until, useImage, whenever } from "@vueuse/core";
import { cond, equals, memoizeWith, pick } from "ramda";
import { defineComponent, getCurrentInstance, onMounted, ref } from "vue";
import { Spin } from "../spin";

type S = "idle" | "loading" | "success" | "failed";

export const ImageEx = defineComponent({
  name: "ludo-image-ex",
  props: {
    src: { type: String, required: true },
    useNaturalSize: { type: Boolean, default: false },
    lazy: { type: Boolean, default: false },
    retryLimit: { type: Number, default: 1 },
    container: { type: String, default: "#app" },
    containerMargin: { type: String, default: "0px" },
  },
  setup(props, ctx) {
    const inst = getCurrentInstance()!.proxy;

    const state = ref<S>("idle");
    let retryTimes = 0;

    onMounted(() => {
      if (props.lazy) {
        withObs(pick(["container", "containerMargin"], props))
          ?.observe({
            el: inst.$el,
            callback() {
              if (state.value === "success") {
                return;
              } else if (state.value === "idle") {
                state.value = "loading";
              } else if (state.value === "failed" && retryTimes < props.retryLimit) {
                state.value = "loading";
                retryTimes++;
              }
            },
          });
      }
    });

    whenever(
      () => !props.lazy && props.src,
      async () => {
        await until(state).toMatch(_ => _ !== "loading");
        state.value = "loading";
      },
      { immediate: true },
    );
    whenever(
      () => state.value === "loading",
      async () => {
        const t = await useImage({ src: props.src });
        state.value = t.error.value ? "failed" : "success";
      },
      { immediate: true },
    );

    function onSuccessLoad(e: Event) {
      if (props.useNaturalSize) {
        const img = e.target as HTMLImageElement;
        img.width = img.naturalWidth / 2;
        img.height = img.naturalHeight / 2;
      } else {
        // pass
      }
    }

    const match = cond([
      [equals("idle"), () => ctx.slots.default?.()],
      [equals("loading"), () => ctx.slots.loading?.() ?? <Spin />],
      [equals("success"), () => <img src={props.src} onLoad={onSuccessLoad} />],
      [equals("failed"), () => ctx.slots.default?.()],
    ]);

    return () => match(state.value);
  },
});

const withObs = memoizeWith(
  (ctx) => `ludo-image-ex_obs_${ctx.container + ctx.containerMargin}`,
  (ctx: {
    container: string;
    containerMargin: string;
  }) => {
    if (!IntersectionObserver) return;

    type ObserveEntity = {
      el: Element;
      callback: () => void;
    };
    const cache = new Map<Element, ObserveEntity>();
    const obs = new IntersectionObserver(
      (entries) => {
        for (const t of entries) {
          if (t.isIntersecting && cache.has(t.target)) {
            cache.get(t.target)?.callback();
          }
        }
      },
      {
        root: document.querySelector(ctx.container)!,
        rootMargin: ctx.containerMargin,
      },
    );

    function observe(payload: ObserveEntity) {
      cache.set(payload.el, payload);
      obs.observe(payload.el);
    }
    function unobserve(el: Element) {
      cache.delete(el);
      obs.unobserve(el);
    }

    return { observe, unobserve };
  },
);
