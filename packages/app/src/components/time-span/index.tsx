import { dayjs } from "~/src/utils/dayjs";
import { computed, defineComponent } from "vue";

import "./style.scss";

/**
 * 显示对应的时间值
 * @prop {number} count - 毫秒级时间值
 * @prop {string} format - 时间格式化字符串
 */
export const TimeSpan = defineComponent({
  name: "ludo-time-span",
  props: {
    count: { type: Number, default: 0 },
    format: { type: String, default: "DD[d]HH[h]mm[m]ss[s]" },
  },
  setup(props) {
    const str = computed(() =>
      dayjs
        .duration(props.count)
        .format(props.format)
    );

    return () => (
      <span class="ludo-time-span" dir="ltr">
        {str
          .value
          .split("")
          .map((ch, i) => <span key={i}>{ch}</span>)}
      </span>
    );
  },
});
