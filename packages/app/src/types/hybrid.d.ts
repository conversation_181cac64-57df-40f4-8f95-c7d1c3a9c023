interface HybridInstance {
  container: "android" | "apple" | "browser";
  checkVersion(): void;
  closeWebView(): void;
  getUserInfo(): Promise<string>;
  jumpAccountInfo(): void;
  jumpAppStore(): void;
  jumpCelebrityPage(): void;
  jumpChat(): void;
  jumpHeadFrame(): void;
  jumpRecharge(): void;
  jumpRoom(): void;
  jumpShoppingPage(): void;
  jumpSubscribe(): void;
  saveImage(): void;
  setCalendar(): void;
  shareOther(): void;
  sharePrivateChat(): void;
  showNativeToast(): void;
  successLoad(): void;
}
