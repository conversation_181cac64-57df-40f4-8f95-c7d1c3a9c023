/* -------------------------------------------------------------------------- */
/*                               vue jsx extend                               */
/* -------------------------------------------------------------------------- */

declare module "vue" {
  interface HTMLAttributes {
    [key: string]: any;
  }
  interface ComponentCustomProps {
    [key: string]: any;
  }
}

/* -------------------------------------------------------------------------- */
/*                                 global vars                                */
/* -------------------------------------------------------------------------- */

declare global {
  var android: any;
  var webkit: any;
  var chrome: any;
  var Hybrid: HybridInstance;
  var __APP_INFO__: APP_INFO;
  var __PAGE_INFO__: PAGE_INFO;
  var getSign: (key: string) => string;
  var getLocalImage: (key: string) => string;
  // webkit callback
  var getInfo: (info: string) => void;
}

/* -------------------------------------------------------------------------- */

export {};
