/// <reference types="@ludoh5/simple-modernizr" />
/// <reference types="@rsbuild/core/types" />

/* -------------------------------------------------------------------------- */
/*                                   modules                                  */
/* -------------------------------------------------------------------------- */

declare module "*.vue" {
  export { default } from "vue";
}

declare module "*.svga" {
  const src: string;
  export default src;
}

declare module "virtual-routes" {
  import type { RouteRecordRaw } from "vue-router";
  const routes: RouteRecordRaw[];
  export default routes;
}

declare module "virtual-image-map" {
  type unit = {
    avif?: string;
    webp?: string;
    base: string;
  };
  const map: Record<string, unit>;
  export default map;
}

/* -------------------------------------------------------------------------- */
/*                                    types                                   */
/* -------------------------------------------------------------------------- */

/**
 * from hybrid inject window.info
 * example: {"package":1,"loginType":0,"nickName":"Guest_13631631","headImage":"https://file.yallaludo.com/DefaultHeadPicture/New/defaultPhoto_12.png","royalLevel":0,"versionName":"*******","prettyId":13631631627,"systemVersion":"12","deviceId":"1face7b8-a630-4e43-b5b1-02a031e6509f","deviceName":"Xiaomi 2201123C","token":"C8FAA3AB92EA866F80B99C2D033D15F9","vipLevel":0,"areaCode":19,"realRoyLevel":0,"phone":"2201123C","operators":"CHINA MOBILE","prettyNumberLevel":0,"idx":113316636,"channelId":1}
 */
interface APP_INFO {
  package: number;
  loginType: number;
  nickName: string;
  headImage: string;
  royalLevel: number;
  versionName: string;
  prettyId: number;
  systemVersion: string;
  deviceId: string;
  deviceName: string;
  token: string;
  vipLevel: number;
  areaCode: number;
  realRoyLevel: number;
  phone: string;
  operators: string;
  prettyNumberLevel: number;
  idx: number;
  channelId: number;
  roomStatus?: number;
}

/**
 * from url search params
 * example: http://************:8080/?activityId=1430&useOriName=1&lang=1&Etype=1&id=1430&type=5078&endTimeSpan=1725093629&showEndTimeSpan=1725093629&hairHeight=6.0"
 */
interface PAGE_INFO {
  id: string;
  adId: string;
  activityId: string;
  useOriName: string;
  lang: string;
  type: string;
  Etype: string;
  endTimeSpan: string;
  showEndTimeSpan: string;
  hairHeight: string;
  isAdv?: string; // 广告入口使用
  carnivalId?: string; // 狂欢节使用
  entryType?: string; // 狂欢节使用
}
