import { mergeDeepRight, reduce } from "ramda";
import Vue from "vue";
import VueI18n from "vue-i18n";
import data_common from "./data/common";
import data_error from "./data/error";
import data_reward from "./data/reward";
import data_unit from "./data/unit";

Vue.use(VueI18n);

export const i18n = new VueI18n({
  locale: "en",
  fallbackLocale: "en",
  silentFallbackWarn: true,
  missing: () => "",
  messages: reduce(mergeDeepRight, {}, [
    data_common,
    data_error,
    data_reward,
    data_unit,
  ]),
  pluralizationRules: {
    /**
     * @param choice {number} 输入给$的选择索引 $tc：`$tc('path.to.rule', choiceIndex)`
     * @param choicesLength {number} 可用选择总数
     * @returns 最终选择索引以选择复数单词
     */
    ar: (choice, choicesLength) => {
      if (choice === 0) return 0;
      if (choice === 1) return 1;
      if (choice === 2) return 2;
      if (choice >= 3 && choice <= 10) return Math.min(3, choicesLength - 1);
      if (choice >= 11 && choice <= 99) return Math.min(4, choicesLength - 1);
      if (choice === 100) return 1;
      if (choice === 180) return Math.min(4, choicesLength - 1);
      return choicesLength - 1;
    },
  },
});
