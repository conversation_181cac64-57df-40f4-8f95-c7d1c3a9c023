// .van-dialog {
//   box-sizing: border-box;
//   overflow: visible !important;
//   background: none !important;
// }

// .van-dialog__message {
//   color: #000;
// }

.van-swipe {
  direction: ltr;

  .van-swipe__track {
    align-items: center;

    .van-swipe-item {
      transform: translate3d(0, 0, 0);
    }
  }
}

.van-toast {
  width: max-content;
  // max-width: 80%;

  &__text {
    word-break: break-word;
    padding: 4px;
  }
}

// #ar .van-toast__text {
//   direction: rtl;
// }

// .van-overlay {
//   background-color: rgba(0, 0, 0, 0.85) !important;
//   transform: translateZ(0);
// }

.van-popup {
  background-color: unset;
}

.van-popover__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.van-popover__arrow {
  top: 1px !important;
  border-bottom-color: #34a7e7 !important;
  border-bottom-width: 10px !important;
  border-left-width: 7px !important;
  border-right-width: 7px !important;

  &::after {
    position: absolute;
    top: 2px;
    left: -6.5px;
    content: "";
    border: 8px solid transparent;
    border-top-width: 0;
    border-bottom-color: #0e1747 !important;
    border-bottom-width: 10px !important;
    border-left-width: 7px !important;
    border-right-width: 7px !important;
  }
}
.van-popover__content {
  background-color: unset !important;
  box-shadow: unset !important;
  border-radius: unset !important;
}
