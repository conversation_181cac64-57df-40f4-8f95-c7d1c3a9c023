@use "./vant.scss";

@font-face {
  font-family: Montserrat;
  src: url("assets/font/BOLD.ttf") format("truetype");
}

html {
  & {
    overscroll-behavior: none;
    -webkit-overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
    
    @apply fixed size-full bg-transparent overflow-hidden select-none;
    @apply text-4;
  }
  &[lang="en"] {
    --global-direction: ltr;
  }
  &[lang="ar"] {
    --global-direction: rtl;
  }

}

body {
  @apply size-full overflow-hidden gdir;
  @apply leading-16 text-(12 white center) font-(bold [Montserrat,Arial,sans-serif]);
}

*,
::after,
::before {
  @apply m-0 p-0 box-border;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  background-color: unset;
  border: unset;
  font-family: inherit;
  font-size: inherit;
}

img {
  object-fit: contain;
}

input,
textarea {
  background: unset;
  border: unset;
}

ul,
ol,
li {
  list-style: none;
}

a,
img,
select {
  -webkit-touch-callout: none;
}

::-webkit-scrollbar {
  display: none;
}
