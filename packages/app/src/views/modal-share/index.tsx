import { useToggle } from "@vueuse/core";
import { memoizeWith } from "ramda";
import { Popup } from "vant";
import { defineComponent, getCurrentInstance } from "vue";
import { useModal } from "~/src/components/modal-wrapper";

const Modal = defineComponent({
  name: "modal-guide",
  setup(props, ctx) {
    const inst = getCurrentInstance()!.proxy;

    const [visible, toggleVisible] = useToggle(false);

    ctx.expose({
      toggleVisible,
      share2GameFriend,
      share2OtherApp,
    });

    function share2GameFriend() {
      toggleVisible(false);
    }
    function share2OtherApp() {
      toggleVisible(false);
    }

    return () => (
      <Popup
        class="h-220 text-center bg-#1f292c/90!"
        value={visible.value}
        position="bottom"
        get-container="#app"
        close-on-click-overlay={false}
        on-close={() => toggleVisible(false)}
        on-click-overlay={() => toggleVisible(false)}
      >
        <p class="mb-18 mt-30 text-(16 #d5e7ec)">{inst.$t("title")}</p>
        <div class="flex-center justify-around text-(14 #f8f0ff) [&_img]:(size-60 p-12 box-content)">
          <div onClick={share2OtherApp}>
            <img src={getLocalImage("icon-share-121x121")} />
            <p>{inst.$t("toApps")}</p>
          </div>
          <div onClick={share2GameFriend}>
            <img src={getLocalImage("icon-friend-121x120")} />
            <p>{inst.$t("toFriends")}</p>
          </div>
        </div>
      </Popup>
    );
  },
  i18n: {
    messages: {
      en: {
        title: "Share to",
        toApps: "Other Apps",
        toFriends: "Friends in Ludo",
      },
      ar: {
        title: "مشاركة مع",
        toApps: "تطبيقات أخرى",
        toFriends: "أصدقاء في لودو",
      },
    },
  },
});

const withModal = memoizeWith(
  () => "modal-share",
  () => useModal({ content: Modal }).create(),
);

export function showModalShare() {
  const inst = withModal();
  inst.toggleVisible(true);
}
