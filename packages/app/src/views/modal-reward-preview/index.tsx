import { useToggle } from "@vueuse/core";
import { memoizeWith } from "ramda";
import { Popup } from "vant";
import { defineComponent, getCurrentInstance } from "vue";
import { useModal } from "~/src/components/modal-wrapper";

const Modal = defineComponent({
  name: "modal-guide",
  setup(props, ctx) {
    const inst = getCurrentInstance()!.proxy;

    const [visible, toggleVisible] = useToggle(false);

    ctx.expose({ toggleVisible });

    return () => (
      <Popup
        class="common-modal top-12.5vh! h-75vh flex-fall"
        value={visible.value}
        get-container="#app"
        close-on-click-overlay={false}
        on-close={() => toggleVisible(false)}
      >
        <div
          class="absolute top-48 left-0 right-0 ms-280 size-44"
          bg="v-icon-x-74x87 ccnr"
          on-click={() => toggleVisible(false)}
        />

        <div
          class="mt-12 leading-24"
          text="19 #FEF8D5 shadows-#D01428"
        >
          {inst.$tc("title")}
        </div>

        <div
          class="py-38 flex-fall"
          text="#B14F35"
        >
          <div>{inst.$tc("t0")}</div>
          <div>{inst.$tc("t1")}</div>
          <div>{inst.$tc("t2")}</div>
          <div>{inst.$tc("t3")}</div>

          <div text="11 #BE7448">{inst.$tc("t4")}</div>

          <button
            class="w-115 h-49 flex-fall leading-45"
            bg="v-btn-yellow-229x97 ccnr"
            on-click={() => toggleVisible(false)}
          >
            <p text="20 #AB6E0F shadows-#FFF08B">{inst.$tc("gonow")}</p>
          </button>
        </div>
      </Popup>
    );
  },
  i18n: {
    messages: {
      en: {
        title: "Tip",
        t0: "How to send room gift (backpack)",
        t1: 'Tap the "Gift" icon',
        t2: 'Tap the "Gift Backpack" icon',
        t3: "Select the gift and send",
        t4: "The tip will only be displayed once",
      },
      ar: {
        title: "النصيحة",
        t0: "كيفية إرسال هدية الغرفة (الحقيبة)",
        t1: 'اضغط على أيقونة "الهدية"',
        t2: 'اضغط على أيقونة "حقيبة الهدايا"',
        t3: "اختر الهدية وأرسلها",
        t4: "سيتم عرض النصيحة مرة واحدة فقط",
      },
    },
  },
});

const withModal = memoizeWith(
  () => "modal-reward-preview",
  () => useModal({ content: Modal }).create(),
);

export function showModalRewardPreview() {
  const inst = withModal();
  inst.toggleVisible(true);
}
