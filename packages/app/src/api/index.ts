import type { SWAGGER_API } from "@ludoh5/swagger-api";
import axios, { type AxiosRequestConfig } from "axios";
import AES from "crypto-js/aes";
import CryptoCore from "crypto-js/core";
import type { RequireAtLeastOne, RequiredDeep } from "type-fest";

const CryptoJS = Object.assign(CryptoCore, { AES });

type ValidUrls = keyof SWAGGER_API;
type ValidPayload<S extends ValidUrls, T = SWAGGER_API[S][0]> = T extends {} ? RequireAtLeastOne<T> : T;
type ValidResponse<S extends ValidUrls, T = SWAGGER_API[S][1]> = T extends {} ? RequiredDeep<T> : T;
type Request = <U extends ValidUrls, E = Record<string, unknown>>(
  url: U,
  data?: ValidPayload<U>,
  config?: AxiosRequestConfig<ValidPayload<U>> & E,
) => Promise<ValidResponse<U>>;

export const http = axios.create({
  baseURL: import.meta.env.DEV ? "/api" : "/",
  timeout: 10000,
  withCredentials: true,
});
export const request: Request = http.post;

/* -------------------------------------------------------------------------- */
/*                                     鉴权                                     */
/* -------------------------------------------------------------------------- */

// request 拦截器
http.interceptors.request.use(
  (config) => {
    console.log("\n请求", config.url);

    // 取值
    const { token, activityId } = Object.assign(
      {},
      window.__APP_INFO__,
      window.__PAGE_INFO__,
    );
    // 初始化
    const random = getRandomString();
    const key = window.getSign(random);
    const iv = key.substring(8, 24);
    const timestamp = Date.now().toString();
    /* ------------------------------- 处理请求header ------------------------------- */
    {
      // 原文
      const source = JSON.stringify({
        timeSpan: timestamp,
        token,
        nonce: getRandomString(),
      });
      console.log("请求参数", source);
      // 密文
      const sign = CryptoJS
        .AES
        .encrypt(
          CryptoJS.enc.Utf8.parse(source),
          CryptoJS.enc.Utf8.parse(key),
          {
            iv: CryptoJS.enc.Utf8.parse(iv),
            mode: CryptoJS.mode.CBC,
          },
        )
        .toString();
      // 赋值
      Object.assign(config.headers, {
        "Access-Token": token, // ? 多此一举？
        "X-Hera": random,
        "X-Zeus": sign,
        "X-Time": timestamp,
      });
    }
    /* -------------------------------- 处理请求body -------------------------------- */
    {
      // 原文
      const source = JSON.stringify(
        Object.assign(config.data ?? {}, { activityId }),
      );
      console.log("请求参数", source);
      // 密文
      const sign = CryptoJS
        .AES
        .encrypt(
          CryptoJS.enc.Utf8.parse(source),
          CryptoJS.enc.Utf8.parse(key),
          {
            iv: CryptoJS.enc.Utf8.parse(iv),
            mode: CryptoJS.mode.CBC,
          },
        )
        .toString();
      // 赋值
      config.data = { paramJsonString: sign };
    }
    /* -------------------------------------------------------------------------- */
    if (config.method === "get") {
      config.url = `${config.url}?${new URLSearchParams(config.data)}`;
    }

    console.log("\n");

    return config;
  },
  (error) => {
    console.error(error.message);
    return Promise.reject(error);
  },
);

/* -------------------------------------------------------------------------- */

function getRandomString() {
  let ret = "";
  const str = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  for (let i = 0; i < 32; i++) {
    ret += str.charAt(Math.floor(Math.random() * str.length));
  }
  return ret;
}

// todo

function getEncryptHeader(key: string) {
  return key.split("").reverse().join("");
}

function getEncryptBody(key: string) {
  return key.split("").reverse().join("");
}

/* -------------------------------------------------------------------------- */
/*                                    错误处理                                    */
/* -------------------------------------------------------------------------- */

import { Toast } from "vant";
import { i18n } from "~/src/i18n";
// import { showModalEnd } from "@/components/modal-end";
// import { showBlacklistTip, showNoRoomTip } from "@/components/modal-tips";

const handler = {
  "801": () => showToast("err_failed_auth", true),
  // "100308": () => showBlacklistTip(), // 金币或者钻石已冻结
  // "100903": () => showModalEnd(), // 活动已结束
  // "101005": () => showModalEnd(), // 活动已结束
  // "108002": () => showNoRoomTip(), // 未创建房间
  "170001": () => showToast("err_failed_collect1"),
  "170002": () => showToast("err_failed_collect1"),
  "170004": () => showToast("err_not_enough_snowballs"),
  "170005": () => showToast("err_low_level"), // 5级以下
  "190001": () => showToast("err_abnormal1"), // 设备号异常
  // "200001": () => showBlacklistTip(), // 黑名单
  // "200009": () => showBlacklistTip(), // 封号账号
  "270006": () => showToast("err_failed_collect4"), // 这个设备已经领取过奖励(设备号)
  "270007": () => showToast("err_failed_operate"), // 游客用户
  "270013": () => showToast("err_failed_collect4"), // 这个设备已经领取过奖励(安卓Id)
  "270014": () => showToast("err_abnormal3"), // 未绑定设备Id; 注册时无安卓Id
  connection: () => showToast("err_failed_connect", true),
  timeout: () => showToast("err_failed_operate", true),
  ip_block: () => showToast("err_abnormal1", true),
};

function showToast(key: string, native = false) {
  if (native) Hybrid.showNativeToast(i18n.tc(key));
  else Toast(i18n.tc(key));
}

http.interceptors.request.use(
  (config) => {
    // 断网提醒
    if (!navigator.onLine) {
      handler.connection();
      return Promise.reject("failed connect");
    }
    return config;
  },
  (error) => {
    console.error(error.message);
    return Promise.reject(error);
  },
);

http.interceptors.response.use(
  (response) => {
    const code = response.data.code as number;
    if (code === 10000) return Promise.resolve(response.data);
    // 处理异常
    (handler as Record<string, () => void>)[`${code}`]?.();
    return Promise.reject(response.data);
  },
  (error) => {
    console.error(error.message);
    // IP 拦截提示
    if (error?.response?.status === 403) handler.ip_block();
    // 超时提醒
    if (error?.message?.includes("timeout")) handler.timeout();
    return Promise.reject(error);
  },
);
