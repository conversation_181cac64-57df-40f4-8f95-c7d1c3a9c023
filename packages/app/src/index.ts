import { i18n } from "~/src/i18n";
import { router } from "~/src/router";
import { VueQueryPlugin } from "@tanstack/vue-query";
import Vue from "vue";
import App from "./App";

import "uno.css";
import "@/styles/index.scss";
import "./index.scss";

Vue.use(VueQueryPlugin);

Modernizr.allsettled.then((ret) => {
  // 在 avif 和 webp 之后再启用默认素材
  document.documentElement.classList.toggle("base", true);

  new Vue({
    el: "#root",
    i18n,
    router,
    render: (h) => h(App),
  });
});
