*[data-hidden] {
  display: none;
}

*[data-invisible] {
  visibility: hidden;
}

#app > .content {
  border-image-source: var(--bg-main-750x867);
  border-image-slice: 640 50% 226;
  border-image-width: 320px 50vw;
  border-image-repeat: stretch;
}

.with-safe-area {
  padding-top: 10px;

  @supports (-webkit-touch-callout: none) {
    padding-top: calc(constant(safe-area-inset-top) - 10vw);
    padding-top: calc(env(safe-area-inset-top) - 10vw);
  }
}

.task-detail {
  @apply grid-(~ rows-[1fr_1.05fr] cols-[1fr_186px_1.1fr] items-center justify-items-center);

  & > :nth-child(1) {
    grid-area: 1 / 1 / 3 / 2;
  }
  & > :nth-child(2) {
    grid-area: 1 / 2 / 2 / 3;
    @apply self-end;
  }
  & > :nth-child(3) {
    grid-area: 2 / 2 / 3 / 3;
    @apply self-start;
  }
  & > :nth-child(4) {
    grid-area: 1 / 3 / 3 / 4;
  }
}

.common-modal {
    border-image-source: var(--bg-modal-common-646x326);
    border-image-slice: 187 50% 103 fill;
    border-image-width: 93.5px 50% 51.5px;
    border-image-repeat: round;
    @apply left-26 w-323 min-h-161 transform-none;
}