class Hybrid4Android implements HybridInstance {
  container = "android" as const;
  native = window.android ?? {};

  checkVersion() {}
  closeWebView() {}
  async getUserInfo() {
    return this.native.getInfo?.() ?? "";
  }
  jumpAccountInfo() {}
  jumpAppStore() {}
  jumpCelebrityPage() {}
  jumpChat() {}
  jumpHeadFrame() {}
  jumpRecharge() {}
  jumpRoom() {}
  jumpShoppingPage() {}
  jumpSubscribe() {}
  saveImage() {}
  setCalendar() {}
  shareOther() {}
  sharePrivateChat() {}
  showNativeToast() {}
  successLoad() {}
}

class Hybrid4Apple implements HybridInstance {
  container = "apple" as const;
  native = window.webkit?.messageHandlers ?? {};

  checkVersion() {}
  closeWebView() {}
  getUserInfo() {
    const ret = Promise.withResolvers<string>();
    window.getInfo = (info: string) => ret.resolve(info);
    this.native.getInfo?.postMessage("getInfo");
    return ret.promise;
  }
  jumpAccountInfo() {}
  jumpAppStore() {}
  jumpCelebrityPage() {}
  jumpChat() {}
  jumpHeadFrame() {}
  jumpRecharge() {}
  jumpRoom() {}
  jumpShoppingPage() {}
  jumpSubscribe() {}
  saveImage() {}
  setCalendar() {}
  shareOther() {}
  sharePrivateChat() {}
  showNativeToast() {}
  successLoad() {}
}

class Hybrid4Browser implements HybridInstance {
  container = "browser" as const;

  checkVersion() {}
  closeWebView() {}
  async getUserInfo() {
    return "";
  }
  jumpAccountInfo() {}
  jumpAppStore() {}
  jumpCelebrityPage() {}
  jumpChat() {}
  jumpHeadFrame() {}
  jumpRecharge() {}
  jumpRoom() {}
  jumpShoppingPage() {}
  jumpSubscribe() {}
  saveImage() {}
  setCalendar() {}
  shareOther() {}
  sharePrivateChat() {}
  showNativeToast() {}
  successLoad() {}
}

/* -------------------------------------------------------------------------- */
/*                                initializing                                */
/* -------------------------------------------------------------------------- */

const ua = window.navigator?.userAgent ?? "";
window.Hybrid = (
  getIsBrowser()
    ? new Hybrid4Browser()
    : getIsAndroid()
    ? new Hybrid4Android()
    : getIsIOS()
    ? new Hybrid4Apple()
    : undefined
) as HybridInstance;

window.Hybrid.getUserInfo().then((info) => {
  window.__APP_INFO__ = info ? JSON.parse(info) : {};
  window.__PAGE_INFO__ = parseLocationSearch();
  console.info("app info", window.__APP_INFO__);
  console.info("page info", window.__PAGE_INFO__);
});

/* -------------------------------------------------------------------------- */

function getIsBrowser() {
  return !!window.chrome?.app;
}

function getIsAndroid() {
  return /Android/.test(ua);
}

function getIsIOS() {
  return (
    /iP(?:ad|hone|od)/.test(ua)
    // The new iPad Pro Gen3 does not identify itself as iPad, but as Macintosh.
    // https://github.com/vueuse/vueuse/issues/3577
    || (window.navigator?.maxTouchPoints > 2 && /iPad|Macintosh/.test(ua))
  );
}

// 获取页面参数（防呆版）
function parseLocationSearch(val = window.location) {
  let searchStr = val.search + val.hash;
  const search = searchStr.match(/\?(\w+=\w+)(&\w+=\w+)*/g) ?? [];
  searchStr = search.map((str, i) => (i > 0 ? str.slice(1) : str)).join("&");
  const payload = Object.fromEntries(new URLSearchParams(searchStr).entries());
  payload.lang ??= "1";
  return payload as unknown as PAGE_INFO;
}
