import ImageMap from "virtual-image-map";

console.info("images", ImageMap);

const styles = Object.entries(ImageMap).reduce(
  (acc, [key, value]) => {
    if (value.avif) acc.avif.push(`--bg-${key}: url("${value.avif}");`);
    if (value.webp) acc.webp.push(`--bg-${key}: url("${value.webp}");`);
    if (value.base) acc.base.push(`--bg-${key}: url("${value.base}");`);
    return acc;
  },
  {
    avif: [] as string[],
    webp: [] as string[],
    base: [] as string[],
  },
);
const styleContent = [
  `html.base {\n${styles.base.join("\n")}\n}`,
  `html.webp {\n${styles.webp.join("\n")}\n}`,
  `html.avif {\n${styles.avif.join("\n")}\n}`,
]
  .join("\n");
const el = document.createElement("style");
el.textContent = styleContent;
document.head.appendChild(el);

window.getLocalImage = (key: string) => {
  const foo = ImageMap[key];
  if (!foo) return "";
  if (Modernizr.avif) return foo.avif ?? foo.base;
  if (Modernizr.webp) return foo.webp ?? foo.base;
  return foo.base;
};
