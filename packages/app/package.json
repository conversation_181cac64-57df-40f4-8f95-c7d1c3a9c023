{"name": "@ludoh5/app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rsbuild build -c build/rsbuild.config.ts", "dev": "rsbuild dev -c build/rsbuild.config.ts --environment development", "preview": "rsbuild preview"}, "dependencies": {"@ludoh5/simple-modernizr": "*", "@ludoh5/unocss-preset-rem2vw": "*", "@tanstack/vue-query": "^5.85.5", "@vueuse/components": "^11.3.0", "@vueuse/core": "^11.3.0", "axios": "^1.11.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "ramda": "^0.31.3", "rxjs": "^7.8.2", "unocss": "^66.4.2", "vant": "^2.13.9", "vconsole": "^3.15.1", "vue": "^2.7.16", "vue-i18n": "^8.28.2", "vue-router": "^3.6.5"}, "devDependencies": {"@rsbuild/core": "^1.4.15", "@rsbuild/plugin-babel": "^1.0.6", "@rsbuild/plugin-sass": "^1.3.5", "@rsbuild/plugin-vue2": "^1.0.4", "@rsbuild/plugin-vue2-jsx": "^1.0.4", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.4", "@types/ramda": "^0.31.0", "@unocss/postcss": "^66.4.2", "@unocss/webpack": "^66.4.2", "fs-extra": "^11.3.1", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sharp": "^0.34.3", "type-fest": "^4.41.0", "typescript": "^5.9.2"}}