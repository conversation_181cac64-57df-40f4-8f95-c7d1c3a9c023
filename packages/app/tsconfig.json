{
  "compilerOptions": {
    "lib": ["DOM", "ESNext"],
    "jsx": "preserve",
    "target": "es5",
    "noEmit": true,
    "skipLibCheck": true,
    "useDefineForClassFields": true,

    /* modules */
    "module": "ESNext",
    "moduleDetection": "force",
    "moduleResolution": "bundler",
    "verbatimModuleSyntax": true,
    "resolveJsonModule": true,
    "allowImportingTsExtensions": true,
    "noUncheckedSideEffectImports": true,

    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,

    "baseUrl": ".",
    "paths": {
      "~/*": ["./*"],
      "@/*": ["src/*"],
      "assets/*": ["src/assets/*"]
    }
  },
  "include": ["lib", "src"]
}
