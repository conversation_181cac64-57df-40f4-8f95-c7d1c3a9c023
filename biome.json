{"$schema": "https://biomejs.dev/schemas/2.1.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "formatWithErrors": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": "off", "complexity": {"noForEach": "off"}, "correctness": {"noConstantCondition": "warn"}, "style": {"noNonNullAssertion": "warn", "useSelfClosingElements": {"level": "warn", "options": {"ignoreHtmlElements": false}}, "useTemplate": {"level": "warn", "fix": "safe"}}, "suspicious": {"noArrayIndexKey": "warn", "noAssignInExpressions": "warn", "noExplicitAny": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "double", "lineWidth": 80, "attributePosition": "multiline"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "off", "useSortedAttributes": "off", "useSortedKeys": "off", "useSortedProperties": "on"}}}}